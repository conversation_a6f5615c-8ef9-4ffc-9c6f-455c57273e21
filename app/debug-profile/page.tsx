"use client"

import { useAuth } from "@/contexts/auth-context"
import { createBrowserClient } from "@/lib/supabase/client"
import { useState } from "react"

export default function DebugProfilePage() {
  const { user, profile, isAuthenticated, isLoading } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [isChecking, setIsChecking] = useState(false)

  const checkProfile = async () => {
    if (!user) {
      console.log("No user available for profile check")
      return
    }

    setIsChecking(true)
    console.log("Starting profile check for user:", user.id)

    try {
      const supabase = createBrowserClient()

      // 直接查詢資料庫
      console.log("Querying profiles table for user:", user.id)
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single()

      console.log("Profile query result:", { data, error })

      setDebugInfo({
        user,
        profile,
        dbQuery: { data, error },
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Profile check error:", error)
      setDebugInfo({
        user,
        profile,
        dbQuery: { error: error },
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsChecking(false)
    }
  }

  const createProfile = async () => {
    if (!user) return
    
    setIsChecking(true)
    try {
      const supabase = createBrowserClient()
      
      const { data, error } = await supabase
        .from("profiles")
        .insert({
          id: user.id,
          name: "站長",
          email: user.email || "",
        })
        .select()
        .single()

      console.log("Manual profile creation:", { data, error })
      
      // 重新檢查
      await checkProfile()
    } catch (error) {
      console.error("Manual profile creation failed:", error)
    } finally {
      setIsChecking(false)
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <h1 className="text-2xl font-bold">Profile Debug</h1>
      
      <div className="space-y-4">
        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">Auth Context State</h2>
          <p>Authenticated: {isAuthenticated ? "Yes" : "No"}</p>
          <p>Loading: {isLoading ? "Yes" : "No"}</p>
          <p>User ID: {user?.id || "None"}</p>
          <p>User Email: {user?.email || "None"}</p>
          <p>Profile Name: {profile?.name || "None"}</p>
          <p>Profile Email: {profile?.email || "None"}</p>
        </div>

        <div className="space-x-2">
          <button 
            onClick={checkProfile}
            disabled={!user || isChecking}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            {isChecking ? "Checking..." : "Check Profile in DB"}
          </button>
          
          <button 
            onClick={createProfile}
            disabled={!user || isChecking}
            className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
          >
            Create Profile Manually
          </button>
        </div>

        {debugInfo && (
          <div className="p-4 border rounded bg-gray-50">
            <h2 className="font-semibold mb-2">Debug Info ({debugInfo.timestamp})</h2>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
