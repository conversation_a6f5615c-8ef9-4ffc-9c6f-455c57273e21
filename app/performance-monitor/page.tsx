"use client"

import { useAuth } from "@/contexts/auth-context"
import { createBrowserClient } from "@/lib/supabase/client"
import { useState, useEffect } from "react"

interface PerformanceMetric {
  operation: string
  duration: number
  timestamp: number
  success: boolean
  error?: string
}

export default function PerformanceMonitorPage() {
  const { user, profile, isAuthenticated, isLoading } = useAuth()
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addMetric = (metric: PerformanceMetric) => {
    setMetrics(prev => [...prev.slice(-19), metric]) // 保留最近20條記錄
  }

  const testProfileQuery = async () => {
    if (!user) return

    const startTime = performance.now()
    try {
      const supabase = createBrowserClient()
      
      const { data, error } = await supabase
        .from("profiles")
        .select("id, name, email, avatar, bio, created_at")
        .eq("id", user.id)
        .single()

      const duration = performance.now() - startTime
      
      addMetric({
        operation: "Profile Query",
        duration: Math.round(duration),
        timestamp: Date.now(),
        success: !error,
        error: error?.message
      })

      return { data, error, duration }
    } catch (error) {
      const duration = performance.now() - startTime
      addMetric({
        operation: "Profile Query",
        duration: Math.round(duration),
        timestamp: Date.now(),
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      })
    }
  }

  const testConnectionSpeed = async () => {
    const startTime = performance.now()
    try {
      const supabase = createBrowserClient()
      
      const { data, error } = await supabase
        .from("topics")
        .select("id")
        .limit(1)

      const duration = performance.now() - startTime
      
      addMetric({
        operation: "Connection Test",
        duration: Math.round(duration),
        timestamp: Date.now(),
        success: !error,
        error: error?.message
      })

      return { data, error, duration }
    } catch (error) {
      const duration = performance.now() - startTime
      addMetric({
        operation: "Connection Test",
        duration: Math.round(duration),
        timestamp: Date.now(),
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      })
    }
  }

  const runBenchmark = async () => {
    setIsRunning(true)
    setMetrics([])

    // 測試連接速度
    await testConnectionSpeed()
    await new Promise(resolve => setTimeout(resolve, 500))

    // 測試 profile 查詢 5 次
    for (let i = 0; i < 5; i++) {
      await testProfileQuery()
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    setIsRunning(false)
  }

  const getAverageTime = (operation: string) => {
    const operationMetrics = metrics.filter(m => m.operation === operation && m.success)
    if (operationMetrics.length === 0) return 0
    return Math.round(operationMetrics.reduce((sum, m) => sum + m.duration, 0) / operationMetrics.length)
  }

  const getSuccessRate = (operation: string) => {
    const operationMetrics = metrics.filter(m => m.operation === operation)
    if (operationMetrics.length === 0) return 0
    const successCount = operationMetrics.filter(m => m.success).length
    return Math.round((successCount / operationMetrics.length) * 100)
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <h1 className="text-2xl font-bold">性能監控</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">認證狀態</h2>
          <p>已認證: {isAuthenticated ? "是" : "否"}</p>
          <p>載入中: {isLoading ? "是" : "否"}</p>
          <p>用戶ID: {user?.id?.slice(0, 8) || "無"}</p>
          <p>Profile: {profile?.name || "無"}</p>
        </div>

        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">連接測試</h2>
          <p>平均時間: {getAverageTime("Connection Test")}ms</p>
          <p>成功率: {getSuccessRate("Connection Test")}%</p>
        </div>

        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">Profile 查詢</h2>
          <p>平均時間: {getAverageTime("Profile Query")}ms</p>
          <p>成功率: {getSuccessRate("Profile Query")}%</p>
        </div>
      </div>

      <div className="space-y-2">
        <button 
          onClick={runBenchmark}
          disabled={isRunning || !user}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {isRunning ? "測試中..." : "開始性能測試"}
        </button>

        <button 
          onClick={testProfileQuery}
          disabled={isRunning || !user}
          className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50 ml-2"
        >
          單次 Profile 查詢
        </button>

        <button 
          onClick={testConnectionSpeed}
          disabled={isRunning}
          className="px-4 py-2 bg-purple-500 text-white rounded disabled:opacity-50 ml-2"
        >
          連接速度測試
        </button>
      </div>

      {metrics.length > 0 && (
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">測試結果</h2>
          <div className="max-h-96 overflow-y-auto">
            {metrics.map((metric, index) => (
              <div 
                key={index} 
                className={`p-2 border rounded text-sm ${
                  metric.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium">{metric.operation}</span>
                  <span className={`font-bold ${
                    metric.duration > 1000 ? 'text-red-600' : 
                    metric.duration > 500 ? 'text-yellow-600' : 'text-green-600'
                  }`}>
                    {metric.duration}ms
                  </span>
                </div>
                {metric.error && (
                  <div className="text-red-600 text-xs mt-1">{metric.error}</div>
                )}
                <div className="text-gray-500 text-xs">
                  {new Date(metric.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="p-4 bg-gray-50 rounded">
        <h2 className="font-semibold mb-2">性能指標說明</h2>
        <ul className="text-sm space-y-1">
          <li><span className="text-green-600">綠色 (&lt;500ms)</span>: 優秀</li>
          <li><span className="text-yellow-600">黃色 (500-1000ms)</span>: 可接受</li>
          <li><span className="text-red-600">紅色 (&gt;1000ms)</span>: 需要優化</li>
        </ul>
      </div>
    </div>
  )
}
