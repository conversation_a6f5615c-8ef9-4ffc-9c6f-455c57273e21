"use client"

import { useState } from "react"
import { TiptapEditor } from "@/components/tiptap-editor"
import { RichTextContent } from "@/components/rich-text-content"

export default function TestEditorPage() {
  const [content, setContent] = useState(`
    <h1>這是標題 1</h1>
    <p>這是一個段落，用來測試編輯器的基本功能。</p>
    
    <h2>這是標題 2</h2>
    <p>這裡有一些文字內容。</p>
    
    <h3>這是標題 3</h3>
    <ul>
      <li>這是無序列表項目 1</li>
      <li>這是無序列表項目 2
        <ul>
          <li>嵌套列表項目 1</li>
          <li>嵌套列表項目 2</li>
        </ul>
      </li>
      <li>這是無序列表項目 3</li>
    </ul>
    
    <ol>
      <li>這是有序列表項目 1</li>
      <li>這是有序列表項目 2</li>
      <li>這是有序列表項目 3</li>
    </ol>
    
    <blockquote>
      <p>這是一個引用塊，用來測試引用樣式。</p>
    </blockquote>
    
    <p>這是包含 <strong>粗體</strong> 和 <em>斜體</em> 的段落。</p>
  `)

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">富文本編輯器測試</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 編輯器 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">編輯器</h2>
          <div className="border rounded-lg p-4">
            <TiptapEditor
              content={content}
              onChange={setContent}
              placeholder="開始輸入內容..."
              minHeight="400px"
            />
          </div>
          
          <div className="text-sm text-muted-foreground">
            <h3 className="font-medium mb-2">測試項目：</h3>
            <ul className="space-y-1">
              <li>• 點擊 H1、H2、H3 按鈕測試標題功能</li>
              <li>• 選擇文字後點擊列表按鈕測試列表功能</li>
              <li>• 在列表中使用 Tab/Shift+Tab 調整縮進</li>
              <li>• 在空列表項按 Enter 退出列表</li>
              <li>• 測試粗體、斜體等格式化功能</li>
            </ul>
          </div>
        </div>
        
        {/* 預覽 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">預覽</h2>
          <div className="border rounded-lg p-4 bg-muted/20">
            <RichTextContent content={content} />
          </div>
          
          <div className="text-sm text-muted-foreground">
            <h3 className="font-medium mb-2">HTML 輸出：</h3>
            <pre className="bg-muted p-2 rounded text-xs overflow-auto max-h-40">
              {content}
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}
