"use client"

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  useSidebar,
} from "@/components/ui/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  BookMarked,
  ChevronLeft,
  ChevronRight,
  Home,
  Plus,
  Search,
  Tag,
  Brain,
  Wrench,
  Rocket,
  Bot,
  LogOut,
  MoreHorizontal,
  User,
  Settings,
  LogIn,
  FileText,
  HelpCircle,
} from "lucide-react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/contexts/auth-context"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { CustomDropdown, CustomDropdownItem, CustomDropdownSeparator } from "@/components/custom-dropdown"
import { useState, useEffect } from "react"
import type { Subtopic } from "@/lib/types"

export function MainSidebar() {
  const pathname = usePathname() || ""
  const router = useRouter()
  const { isAuthenticated, user, profile, signOut, isLoading, refreshProfile } = useAuth()
  const { collapsed, toggleSidebar } = useSidebar()

  // 核心主題
  const coreTopics = [
    { name: "LLM", href: "/topic/llm", icon: <Brain className="h-4 w-4" /> },
    { name: "Prompt Engineering", href: "/topic/prompt-engineering", icon: <Wrench className="h-4 w-4" /> },
    { name: "RAG", href: "/topic/rag", icon: <Rocket className="h-4 w-4" /> },
    { name: "Text-to-Video", href: "/topic/text-to-video", icon: <Bot className="h-4 w-4" /> },
  ]

  // Add these state variables and useEffect hook inside the MainSidebar function:
  const [hotTags, setHotTags] = useState<Array<{ name: string; href: string }>>([])
  const [isLoadingTags, setIsLoadingTags] = useState(true)

  // 處理生產環境下 profile 為空的情況
  useEffect(() => {
    if (isAuthenticated && user && !profile && !isLoading) {
      console.log("MainSidebar: User authenticated but profile missing, attempting to refresh")
      // 延遲一點時間再嘗試刷新，避免過於頻繁的請求
      const timer = setTimeout(() => {
        if (refreshProfile) {
          refreshProfile()
        }
      }, 1000) // 增加延遲時間，給第一次查詢更多時間

      return () => clearTimeout(timer)
    }
  }, [isAuthenticated, user, profile, isLoading, refreshProfile])

  useEffect(() => {
    const fetchPopularSubtopics = async () => {
      try {
        setIsLoadingTags(true)

        // Add timeout to prevent infinite loading if the request hangs
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000) // 增加到 10 秒

        // Use fetch with abort controller
        const response = await fetch("/api/subtopics/popular?limit=5", {
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        // Handle non-OK responses
        if (!response.ok) {
          const errorText = await response.text()
          console.error(`Error fetching popular subtopics: ${response.status} - ${errorText}`)
          throw new Error(`Error ${response.status}: ${errorText}`)
        }

        // Parse JSON with error handling
        let data
        try {
          data = await response.json()
        } catch (parseError) {
          console.error("Error parsing JSON response:", parseError)
          throw new Error("Invalid response format")
        }

        if (data.success && data.data) {
          // Transform the data to match the format we need
          const formattedTags = data.data.map((subtopic: Subtopic) => {
            const topicSlug = (subtopic as any).topics?.slug || ""
            return {
              name: `#${subtopic.name}`,
              href: `/topic/${topicSlug}/${subtopic.slug || encodeURIComponent(subtopic.name)}`,
            }
          })
          setHotTags(formattedTags)
        } else {
          // Handle case where data.success is false
          console.warn("API returned unsuccessful response:", data.error)
          throw new Error(data.error || "Failed to fetch popular subtopics")
        }
      } catch (error) {
        // 改善錯誤處理，不顯示 AbortError
        if (error instanceof Error && error.name === 'AbortError') {
          console.warn("Request timed out, using fallback tags")
        } else {
          console.error("Error fetching popular subtopics:", error)
        }

        // Set fallback tags in case of error
        setHotTags([
          { name: "#RAG", href: "/topic/rag" },
          { name: "#微調", href: `/topic/llm/${encodeURIComponent("微調")}` },
          { name: "#提示工程", href: "/topic/prompt-engineering" },
          { name: "#機器學習", href: "/topic/machine-learning" },
        ])
      } finally {
        setIsLoadingTags(false)
      }
    }

    fetchPopularSubtopics()
  }, [])

  return (
    <Sidebar className="border-r bg-background">
      <SidebarHeader className="p-3 flex items-center justify-between">
        <Link href="/" className="flex items-center gap-2">
          <div className="flex">
            {/* 調整 Logo 大小，確保在折疊狀態下不會溢出 */}
            <div
              className={`h-4 w-4 bg-amber-300 rounded-sm translate-x-0.5 translate-y-0.5 ${collapsed ? "scale-75" : ""}`}
            ></div>
            <div
              className={`h-4 w-4 bg-amber-400 rounded-sm -translate-x-0.5 -translate-y-0.5 ${collapsed ? "scale-75" : ""}`}
            ></div>
          </div>
          {!collapsed && <span className="text-lg font-medium">AILogora</span>}
        </Link>

        <Button
          variant={collapsed ? "sidebar-icon" : "ghost"}
          className={cn(collapsed ? "w-7 h-7 rounded-full" : "h-7 w-7", "flex items-center justify-center p-0")}
          onClick={toggleSidebar}
        >
          {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </SidebarHeader>

      <SidebarContent className="px-2 py-1">
        {/* 搜尋框 */}
        {!collapsed && (
          <div className="mb-3 relative">
            <Input placeholder="搜尋..." className="pl-8 h-7 text-sm bg-muted/40 border-none rounded-md" />
            <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
          </div>
        )}

        {/* 主要導航 */}
        <SidebarMenu className="px-0">
          <SidebarMenuItem>
            <Link href="/" className="w-full">
              <SidebarMenuButton isActive={pathname === "/"} className="h-7 py-1.5 text-sm">
                <Home className="h-4 w-4" />
                {!collapsed && <span className="ml-2">首頁</span>}
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <Link href="/explore" className="w-full">
              <SidebarMenuButton isActive={pathname.startsWith("/explore")} className="h-7 py-1.5 text-sm">
                <Search className="h-4 w-4" />
                {!collapsed && <span className="ml-2">自由探索</span>}
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <Link href="/library" className="w-full">
              <SidebarMenuButton isActive={pathname.startsWith("/library")} className="h-7 py-1.5 text-sm">
                <BookMarked className="h-4 w-4" />
                {!collapsed && <span className="ml-2">我的收藏</span>}
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <Link href="/my-posts" className="w-full">
              <SidebarMenuButton isActive={pathname.startsWith("/my-posts")} className="h-7 py-1.5 text-sm">
                <FileText className="h-4 w-4" />
                {!collapsed && <span className="ml-2">我的發表</span>}
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <Link href="/submit" className="w-full">
              <SidebarMenuButton isActive={pathname === "/submit"} className="h-7 py-1.5 text-sm">
                <Plus className="h-4 w-4" />
                {!collapsed && <span className="ml-2">投稿觀點卡</span>}
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        </SidebarMenu>

        <Separator className="my-3 bg-muted/60" />

        {/* 核心主題 */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs text-muted-foreground font-medium px-2 py-1">
            {!collapsed ? "核心主題" : ""}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="px-0">
              {coreTopics.map((topic) => (
                <SidebarMenuItem key={topic.href}>
                  <Link href={topic.href} className="w-full">
                    <SidebarMenuButton
                      isActive={pathname === topic.href || (!!pathname && pathname.startsWith(`${topic.href}/`))}
                      className="h-7 py-1.5 text-sm"
                    >
                      {topic.icon}
                      {!collapsed && <span className="ml-2">{topic.name}</span>}
                    </SidebarMenuButton>
                  </Link>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-3 bg-muted/60" />

        {/* 熱門標籤 */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs text-muted-foreground font-medium px-2 py-1">
            {!collapsed ? "熱門標籤" : ""}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="px-0">
              {isLoadingTags ? (
                // Show loading indicator for tags
                <div className="flex justify-center py-2">
                  <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-primary/60 animate-spin"></div>
                </div>
              ) : hotTags.length > 0 ? (
                // Show tags if available
                hotTags.map((tag) => (
                  <SidebarMenuItem key={tag.href}>
                    <Link href={tag.href} className="w-full">
                      <SidebarMenuButton
                        isActive={pathname === tag.href || (!!pathname && pathname.startsWith(`${tag.href}/`))}
                        className="h-7 py-1.5 text-sm"
                      >
                        <Tag className="h-4 w-4" />
                        {!collapsed && <span className="ml-2">{tag.name}</span>}
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                ))
              ) : (
                // Show message if no tags available
                <div className="px-2 py-1 text-xs text-muted-foreground">{!collapsed && "暫無熱門標籤"}</div>
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-3 mt-auto border-t border-muted/40">
        {/* 關於我們連結 */}
        <SidebarMenu className="px-0 mb-3">
          <SidebarMenuItem>
            <Link href="/about" className="w-full">
              <SidebarMenuButton isActive={pathname === "/about"} className="h-7 py-1.5 text-sm">
                <HelpCircle className="h-4 w-4" />
                {!collapsed && <span className="ml-2">關於我們</span>}
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        </SidebarMenu>

        {isLoading ? (
          // 顯示更小、更不引人注目的載入指示器
          <div className="flex items-center justify-center w-full py-1">
            <div className="h-5 w-5 rounded-full border-2 border-t-transparent border-primary/60 animate-spin"></div>
          </div>
        ) : isAuthenticated ? (
          // 用戶已認證 - 顯示個人資料下拉選單
          <CustomDropdown
            trigger={
              <div className="flex items-center justify-between cursor-pointer hover:bg-accent/50 rounded-md p-1 transition-colors">
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage
                      src={
                        profile?.avatar || `/placeholder.svg?height=24&width=24&query=${profile?.name || "User"}`
                      }
                      alt={profile?.name || "User"}
                    />
                    <AvatarFallback className="text-xs">
                      {profile?.name?.substring(0, 2) || user?.email?.substring(0, 2) || "AI"}
                    </AvatarFallback>
                  </Avatar>

                  {!collapsed && (
                    <div className="flex flex-col">
                      <span className="font-medium text-xs">{profile?.name || "使用者"}</span>
                      <span className="text-[10px] text-muted-foreground truncate max-w-[8rem]">{user?.email}</span>
                    </div>
                  )}
                </div>

                {!collapsed && <MoreHorizontal className="h-3.5 w-3.5" />}
              </div>
            }
            align="end"
            className="bottom-full top-auto mb-2"
          >
            <CustomDropdownItem>
              <Link href="/profile" className="flex items-center w-full">
                <User className="mr-2 h-4 w-4" />
                <span>個人資料</span>
              </Link>
            </CustomDropdownItem>
            <CustomDropdownItem>
              <Link href="/settings" className="flex items-center w-full">
                <Settings className="mr-2 h-4 w-4" />
                <span>設定</span>
              </Link>
            </CustomDropdownItem>
            <CustomDropdownSeparator />
            <CustomDropdownItem
              onClick={() => {
                // 點擊後立即呼叫 signOut，若失敗寫入 console
                signOut().catch((error) => {
                  console.error("Error during sign out:", error)
                })
              }}
            >
              <div className="flex items-center">
                <LogOut className="mr-2 h-4 w-4" />
                <span>登出</span>
              </div>
            </CustomDropdownItem>
          </CustomDropdown>
        ) : (
          // 用戶未認證 - 顯示登入按鈕
          <Link href="/auth/login" className="w-full flex justify-center">
            {collapsed ? (
              <div className="flex h-8 w-8 items-center justify-center rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground">
                <LogIn className="h-4 w-4" />
              </div>
            ) : (
              <div className="flex h-7 w-full items-center justify-center rounded-md border border-input bg-background px-3 text-xs font-medium hover:bg-accent hover:text-accent-foreground">
                登入
              </div>
            )}
          </Link>
        )}
      </SidebarFooter>
    </Sidebar>
  )
}
