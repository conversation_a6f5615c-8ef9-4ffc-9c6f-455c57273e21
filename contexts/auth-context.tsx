"use client"

import type React from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { forceAuthSync } from "@/lib/auth-sync"
import { useRouter } from "next/navigation"
import { createContext, useContext, useEffect, useState, useMemo, useRef, useCallback } from "react"

type Profile = {
  id: string
  name: string
  email: string
  avatar?: string | null
  bio?: string
  created_at?: string
  updated_at?: string
}

type User = {
  id: string
  email: string
}

type AuthContextType = {
  user: User | null
  profile: Profile | null
  isAuthenticated: boolean
  isLoading: boolean
  signUp: (email: string, password: string, name: string) => Promise<{ error: Error | null }>
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>
  signInWithProvider: (provider: "google" | "facebook" | "github") => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  updateProfile: (data: Partial<Profile>) => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  profile: null,
  isAuthenticated: false,
  isLoading: true,
  signUp: async () => ({ error: null }),
  signIn: async () => ({ error: null }),
  signInWithProvider: async () => { },
  signOut: async () => { },
  refreshProfile: async () => { },
  updateProfile: async () => { },
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isFetchingProfile, setIsFetchingProfile] = useState(false)

  // 添加 profile 緩存
  const profileCacheRef = useRef<{ [userId: string]: { profile: Profile, timestamp: number } }>({})
  const router = useRouter()

  // 創建 Supabase 客戶端
  const supabase = useMemo(() => createBrowserClient(), [])

  // 獲取用戶資料
  const fetchProfile = async (userId: string) => {
    // 防止重複調用
    if (isFetchingProfile) {
      console.log("fetchProfile: Already fetching profile, skipping...")
      return
    }

    // 檢查緩存（5分鐘內有效）
    const cached = profileCacheRef.current[userId]
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
      console.log("fetchProfile: Using cached profile for user:", userId)
      setProfile(cached.profile)
      return
    }

    try {
      setIsFetchingProfile(true)
      console.log("fetchProfile: Starting for user:", userId)

      // 在生產環境下增加超時時間，開發環境保持較短
      const timeoutDuration = process.env.NODE_ENV === 'production' ? 15000 : 8000 // 減少超時時間

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timeout')), timeoutDuration)
      })

      const queryPromise = supabase
        .from("profiles")
        .select("id, name, email, avatar, bio, created_at") // 只選擇需要的欄位
        .eq("id", userId)
        .single()

      console.log("fetchProfile: Executing query...")
      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any

      console.log("fetchProfile: Query result:", {
        data,
        error,
        hasData: !!data,
        errorCode: error?.code,
        errorMessage: error?.message
      })

      if (error) {
        console.error("fetchProfile: Error fetching profile:", error)

        // 如果找不到資料，嘗試創建
        if (error.code === 'PGRST116') { // 記錄不存在
          console.log("fetchProfile: Profile not found, attempting to create...")
          const { data: userData } = await supabase.auth.getUser()
          if (userData?.user && userData.user.id === userId) {
            console.log("fetchProfile: Creating new profile for user:", userId)
            const { data: newProfile, error: insertError } = await supabase
              .from("profiles")
              .insert({
                id: userId,
                name: userData.user.user_metadata?.name || userData.user.email?.split('@')[0] || "用戶",
                email: userData.user.email || "",
              })
              .select("id, name, email, avatar, bio, created_at")
              .single()

            console.log("fetchProfile: Profile creation result:", { newProfile, insertError })

            if (!insertError && newProfile) {
              console.log("fetchProfile: Profile created successfully:", newProfile)
              const profileData = newProfile as Profile
              setProfile(profileData)
              // 緩存新創建的 profile
              profileCacheRef.current[userId] = {
                profile: profileData,
                timestamp: Date.now()
              }
            } else {
              console.error("fetchProfile: Failed to create profile:", insertError)
            }
          }
        } else {
          console.error("fetchProfile: Profile fetch error (not PGRST116):", error.code, error.message)
        }
      } else if (data) {
        console.log("fetchProfile: Profile found:", data)
        const profileData = data as Profile
        setProfile(profileData)
        // 緩存 profile
        profileCacheRef.current[userId] = {
          profile: profileData,
          timestamp: Date.now()
        }
      } else {
        console.warn("fetchProfile: No data and no error returned from profile query")
      }
    } catch (error) {
      console.error("fetchProfile: Catch block error:", error)

      // 如果是超時錯誤，在生產環境下不要立即重試，避免重複調用
      if (error instanceof Error && error.message === 'Profile fetch timeout') {
        console.warn("fetchProfile: Query timed out, profile will be loaded by subsequent calls")
      }
    } finally {
      setIsFetchingProfile(false)
    }
  }

  // 初始化認證狀態
  useEffect(() => {
    let isMounted = true // 防止組件卸載後狀態更新

    const initAuth = async () => {
      try {
        setIsLoading(true)
        console.log("InitAuth: Starting authentication check")

        // 先檢查 session，再獲取用戶
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.log("InitAuth: Session error", sessionError.message)
          setUser(null)
          setProfile(null)
          setIsAuthenticated(false)
          return
        }

        let user = session?.user || null
        let error = null

        // 如果 session 存在但沒有 user，嘗試獲取 user 資訊
        if (session && !user) {
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Authentication check timeout')), 5000)
          })

          try {
            const authPromise = supabase.auth.getUser()
            const userResult = await Promise.race([authPromise, timeoutPromise]) as any
            user = userResult.data?.user || null
            error = userResult.error
          } catch (timeoutError) {
            console.warn("InitAuth: getUser timeout, using session user")
            user = session.user
          }
        }

        if (!isMounted) return // 如果組件已卸載，停止執行

        console.log("InitAuth: getUser result", { hasUser: !!user, error: error?.message })

        if (user && !error) {
          const currentUser = {
            id: user.id,
            email: user.email || "",
          }
          setUser(currentUser)
          setIsAuthenticated(true)
          console.log("InitAuth: User authenticated", { userId: user.id.slice(0, 8) })

          // 獲取用戶資料 - 在生產環境下增加重試機制
          try {
            await fetchProfile(user.id)
          } catch (profileError) {
            console.error("InitAuth: Profile fetch failed, retrying...", profileError)
            // 在生產環境下，如果第一次獲取失敗，延遲後重試
            if (process.env.NODE_ENV === 'production') {
              setTimeout(async () => {
                if (isMounted) {
                  try {
                    await fetchProfile(user.id)
                  } catch (retryError) {
                    console.error("InitAuth: Profile retry failed", retryError)
                  }
                }
              }, 1000)
            }
          }
        } else {
          setUser(null)
          setProfile(null)
          setIsAuthenticated(false)
          console.log("InitAuth: No user found")
        }
      } catch (error) {
        console.error("Error initializing auth:", error)
        if (isMounted) {
          setUser(null)
          setProfile(null)
          setIsAuthenticated(false)
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
          console.log("InitAuth: Loading complete")
        }
      }
    }

    initAuth()

    return () => {
      isMounted = false
    }
  }, [])

  // 簡潔的跨標籤頁同步機制
  useEffect(() => {
    if (typeof window === 'undefined') return

    // 監聽 storage 變化和窗口焦點
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key?.includes('sb-auth-token')) {
        console.log("Cross-tab sync: Auth token changed", e.key)
        // 延遲檢查，讓 Supabase 內部狀態更新完成
        setTimeout(async () => {
          try {
            const { data: { session } } = await supabase.auth.getSession()

            // 只在必要時更新狀態，避免不必要的同步
            if (session && session.user && !isAuthenticated) {
              console.log("Cross-tab sync: Found session, updating state")
              const currentUser = {
                id: session.user.id,
                email: session.user.email || "",
              }
              setUser(currentUser)
              setIsAuthenticated(true)
              setIsLoading(false)
              await fetchProfile(session.user.id)
            } else if (!session && isAuthenticated && user) {
              console.log("Cross-tab sync: No session, clearing state")
              setUser(null)
              setProfile(null)
              setIsAuthenticated(false)
              setIsLoading(false)
            }
          } catch (error) {
            console.error("Cross-tab sync error:", error)
          }
        }, 300)
      }
    }

    const handleWindowFocus = () => {
      // 當窗口重新獲得焦點時，快速檢查認證狀態
      if (isLoading) {
        console.log("Cross-tab sync: Window focused while loading, checking state")
        setTimeout(async () => {
          try {
            const { data: { session } } = await supabase.auth.getSession()
            if (session) {
              const currentUser = {
                id: session.user.id,
                email: session.user.email || "",
              }
              setUser(currentUser)
              setIsAuthenticated(true)
              setIsLoading(false)
              await fetchProfile(session.user.id)
            } else {
              setIsLoading(false)
            }
          } catch (error) {
            console.error("Focus sync error:", error)
            setIsLoading(false)
          }
        }, 200)
      }
    }

    window.addEventListener('storage', handleStorageChange)
    window.addEventListener('focus', handleWindowFocus)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('focus', handleWindowFocus)
    }
  }, [isAuthenticated, isLoading])

  // 監聽認證狀態變化
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log("Auth state changed:", event, "Session exists:", !!session)

        try {
          if (event === 'SIGNED_IN' && session) {
            const currentUser = {
              id: session.user.id,
              email: session.user.email || "",
            }
            setUser(currentUser)
            setIsAuthenticated(true)
            setIsLoading(false) // 確保 loading 狀態被設置為 false
            await fetchProfile(session.user.id)
            console.log("Auth state: SIGNED_IN complete")
          } else if (event === 'SIGNED_OUT') {
            setUser(null)
            setProfile(null)
            setIsAuthenticated(false)
            setIsLoading(false) // 確保 loading 狀態被設置為 false
            console.log("Auth state: SIGNED_OUT complete")
          } else if (event === 'TOKEN_REFRESHED' && session) {
            // Token 刷新時，保持用戶狀態並重新獲取 profile
            const currentUser = {
              id: session.user.id,
              email: session.user.email || "",
            }
            setUser(currentUser)
            setIsAuthenticated(true)
            setIsLoading(false) // 確保 loading 狀態被設置為 false

            // 在生產環境下，TOKEN_REFRESHED 時確保重新獲取 profile
            try {
              await fetchProfile(session.user.id)
            } catch (profileError) {
              console.error("TOKEN_REFRESHED: Profile fetch failed", profileError)
              // 在生產環境下重試
              if (process.env.NODE_ENV === 'production') {
                setTimeout(async () => {
                  try {
                    await fetchProfile(session.user.id)
                  } catch (retryError) {
                    console.error("TOKEN_REFRESHED: Profile retry failed", retryError)
                  }
                }, 500)
              }
            }
            console.log("Auth state: TOKEN_REFRESHED complete")
          } else if (event === 'INITIAL_SESSION') {
            if (session) {
              const currentUser = {
                id: session.user.id,
                email: session.user.email || "",
              }
              setUser(currentUser)
              setIsAuthenticated(true)

              // 在生產環境下，INITIAL_SESSION 時確保正確獲取 profile
              try {
                await fetchProfile(session.user.id)
              } catch (profileError) {
                console.error("INITIAL_SESSION: Profile fetch failed", profileError)
                // 在生產環境下重試
                if (process.env.NODE_ENV === 'production') {
                  setTimeout(async () => {
                    try {
                      await fetchProfile(session.user.id)
                    } catch (retryError) {
                      console.error("INITIAL_SESSION: Profile retry failed", retryError)
                    }
                  }, 500)
                }
              }
              console.log("Auth state: INITIAL_SESSION with user")
            } else {
              setUser(null)
              setProfile(null)
              setIsAuthenticated(false)
              console.log("Auth state: INITIAL_SESSION without user")
            }
            setIsLoading(false)
          } else {
            // 其他事件，確保 loading 狀態被正確設置
            setIsLoading(false)
            console.log("Auth state: Other event", event)
          }
        } catch (error) {
          console.error("Error in auth state change handler:", error)
          setIsLoading(false) // 即使出錯也要停止 loading
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  // 註冊
  const signUp = async (email: string, password: string, name: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      })

      if (error) {
        return { error }
      }

      if (data.user) {
        const currentUser = {
          id: data.user.id,
          email: data.user.email || "",
        }
        setUser(currentUser)
        setIsAuthenticated(true)

        // 創建用戶資料
        await supabase
          .from("profiles")
          .insert({
            id: data.user.id,
            name,
            email: data.user.email || "",
          })
      }

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  }

  // 登入
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { error }
      }

      if (data.user) {
        const currentUser = {
          id: data.user.id,
          email: data.user.email || "",
        }
        setUser(currentUser)
        setIsAuthenticated(true)
        await fetchProfile(data.user.id)

        // 在生產環境中強制同步身份驗證狀態
        if (process.env.NODE_ENV === 'production') {
          setTimeout(async () => {
            const syncResult = await forceAuthSync()
            if (!syncResult.success) {
              console.warn('Post-login auth sync failed:', syncResult.error)
            }
          }, 200)
        }
      }

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  }

  // OAuth 登入
  const signInWithProvider = async (provider: "google" | "facebook" | "github") => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        console.error(`Error signing in with ${provider}:`, error)
      }
    } catch (error) {
      console.error(`Error in signInWithProvider (${provider}):`, error)
    }
  }

  // 登出
  const signOut = async () => {
    try {
      console.log("開始登出...")
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error("Supabase signOut error:", error)
        // 即使 Supabase 登出失敗，也要清除本地狀態
      }

      // 清除本地狀態
      setUser(null)
      setProfile(null)
      setIsAuthenticated(false)

      console.log("登出成功，重定向到首頁...")
      router.push("/")
    } catch (error) {
      console.error("Error signing out:", error)
      // 即使發生錯誤，也要清除本地狀態
      setUser(null)
      setProfile(null)
      setIsAuthenticated(false)
      router.push("/")
    }
  }

  // 刷新用戶資料
  const refreshProfile = async () => {
    if (user) {
      // 清除緩存以強制重新獲取
      delete profileCacheRef.current[user.id]
      await fetchProfile(user.id)
    }
  }

  // 更新用戶資料
  const updateProfile = async (data: Partial<Profile>) => {
    if (!user) {
      throw new Error("User not authenticated")
    }

    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          ...data,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id)

      if (error) {
        throw error
      }

      await refreshProfile()
    } catch (error) {
      console.error("Error updating profile:", error)
      throw error
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isAuthenticated,
        isLoading,
        signUp,
        signIn,
        signInWithProvider,
        signOut,
        refreshProfile,
        updateProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
