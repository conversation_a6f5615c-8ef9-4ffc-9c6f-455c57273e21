{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "docs:generate": "bun ./scripts/generate-api-docs.js", "docs:analyze": "bun ./scripts/add-api-docs-template.js", "docs:dev": "bun run docs:generate && bun run dev", "docs:serve": "bun run docs:generate && bun run start"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "latest", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "latest", "@tiptap/core": "latest", "@tiptap/extension-image": "latest", "@tiptap/extension-link": "latest", "@tiptap/extension-placeholder": "latest", "@tiptap/pm": "latest", "@tiptap/react": "latest", "@tiptap/starter-kit": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-swagger-doc": "^0.4.1", "next-themes": "latest", "react": "19.1.0", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "react-hook-form": "latest", "react-icons": "latest", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "styled-jsx": "^5.1.7", "swagger-ui-react": "^5.24.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "latest"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/swagger-ui-react": "^5.18.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}